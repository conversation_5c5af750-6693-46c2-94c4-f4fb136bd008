#!/bin/bash
# Simple CXI PMD build script using local headers

set -e

# Configuration
DPDK_DIR="$HOME/development/dpdk"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    echo_info "Checking prerequisites..."
    
    # Check if DPDK directory exists
    if [ ! -d "$DPDK_DIR" ]; then
        echo_error "DPDK directory not found: $DPDK_DIR"
        echo_info "Please clone DPDK first: git clone https://dpdk.org/git/dpdk $DPDK_DIR"
        exit 1
    fi
    
    # Check if CXI PMD exists
    if [ ! -d "$DPDK_DIR/drivers/net/cxi" ]; then
        echo_error "CXI PMD directory not found: $DPDK_DIR/drivers/net/cxi"
        exit 1
    fi
    
    # Check if local headers exist
    if [ ! -d "$DPDK_DIR/drivers/net/cxi/include" ]; then
        echo_error "CXI PMD include directory not found: $DPDK_DIR/drivers/net/cxi/include"
        exit 1
    fi
    
    # Check for required headers
    required_headers=(
        "$DPDK_DIR/drivers/net/cxi/include/libcxi.h"
        "$DPDK_DIR/drivers/net/cxi/include/cxi_prov_hw.h"
        "$DPDK_DIR/drivers/net/cxi/include/cassini_user_defs.h"
    )
    
    for header in "${required_headers[@]}"; do
        if [ ! -f "$header" ]; then
            echo_error "Required header not found: $header"
            exit 1
        fi
    done
    
    echo_success "Prerequisites check passed"
}

# Build DPDK with CXI PMD
build_dpdk() {
    echo_info "Building DPDK with CXI PMD..."
    
    cd "$DPDK_DIR"
    
    # Clean previous build
    if [ -d "build" ]; then
        echo_info "Cleaning previous build..."
        rm -rf build
    fi
    
    # Configure build
    echo_info "Configuring DPDK build..."
    meson setup build \
        -Dexamples=all \
        -Dtests=true \
        -Ddeveloper_mode=true \
        -Denable_drivers=net/cxi \
        -Dwerror=false
    
    # Build
    echo_info "Building DPDK (this may take a while)..."
    ninja -C build -j$(nproc)
    
    echo_success "DPDK build completed"
}

# Verify CXI PMD build
verify_build() {
    echo_info "Verifying CXI PMD build..."
    
    # Check if CXI PMD library exists
    CXI_LIB=$(find "$DPDK_DIR/build" -name "*cxi*" -type f 2>/dev/null | head -1)
    
    if [ -n "$CXI_LIB" ]; then
        echo_success "CXI PMD built successfully!"
        echo_info "CXI PMD files found:"
        find "$DPDK_DIR/build" -name "*cxi*" -type f
        
        # Test if CXI PMD is available in testpmd
        if [ -f "$DPDK_DIR/build/app/dpdk-testpmd" ]; then
            echo_info "Testing CXI PMD availability in testpmd..."
            if sudo "$DPDK_DIR/build/app/dpdk-testpmd" --help 2>/dev/null | grep -q "cxi\|CXI"; then
                echo_success "CXI PMD is available in testpmd"
            else
                echo_warning "CXI PMD may not be properly registered"
            fi
        fi
    else
        echo_error "CXI PMD not found in build output"
        echo_info "Check build logs for errors:"
        echo_info "cat $DPDK_DIR/build/meson-logs/meson-log.txt | grep -i cxi"
        return 1
    fi
}

# Show header information
show_header_info() {
    echo_info "CXI PMD Header Information:"
    echo_info "Using local headers from: $DPDK_DIR/drivers/net/cxi/include/"
    
    echo_info "Available headers:"
    ls -la "$DPDK_DIR/drivers/net/cxi/include/"*.h
    
    echo_info "Header sizes:"
    wc -l "$DPDK_DIR/drivers/net/cxi/include/"*.h
}

# Main execution
main() {
    echo_info "Starting CXI PMD build process with local headers..."
    
    check_prerequisites
    show_header_info
    build_dpdk
    verify_build
    
    echo_success "CXI PMD build process completed successfully!"
    echo_info "The CXI PMD is now ready for use with DPDK applications"
    echo_info "Example: sudo $DPDK_DIR/build/app/dpdk-testpmd -l 0-1 -n 4"
    echo_warning "Note: Runtime requires libcxi library to be installed separately"
}

# Run main function
main "$@"
